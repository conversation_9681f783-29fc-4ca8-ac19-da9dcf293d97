from train import train
from evaluate import test, test_with_metrics
from data_processing import create_dataloaders
from arguments import parse_args
from model import *
from plotting import *
from xgboost_utils import create_xgboost_data, create_xgboost_model, train_xgboost
from sklearn_utils import create_sklearn_data, create_linear_regression_model, create_gaussian_process_model, train_sklearn_model
import torch.nn as nn
import torch
import pandas as pd

if __name__ == '__main__':
    device = 'cuda' if torch.cuda.is_available() else 'cpu'

    args = parse_args()

    if args.model_name == 'xgboost':
        print("Using XGBoost model")
        # Use XGBoost-specific data processing
        X_train, y_train, X_test, y_test = create_xgboost_data(args)
        model = create_xgboost_model(args)

        # Train XGBoost model
        train_losses, test_losses, test_acc = train_xgboost(X_train, y_train, X_test, y_test, model, args)
        epochs = 1  # XGBoost trains in one go

    elif args.model_name == 'linear_regression':
        print("Using Linear Regression model")
        # Use sklearn-specific data processing
        X_train, y_train, X_test, y_test = create_sklearn_data(args)
        model = create_linear_regression_model(args)

        # Train Linear Regression model
        train_losses, test_losses, test_acc = train_sklearn_model(X_train, y_train, X_test, y_test, model, args)
        epochs = 1  # Linear Regression trains in one go

    elif args.model_name == 'gaussian_process':
        print("Using Gaussian Process model")
        # Use sklearn-specific data processing
        X_train, y_train, X_test, y_test = create_sklearn_data(args)
        model = create_gaussian_process_model(args)

        # Train Gaussian Process model
        train_losses, test_losses, test_acc = train_sklearn_model(X_train, y_train, X_test, y_test, model, args)
        epochs = 1  # Gaussian Process trains in one go

    else:
        # Use PyTorch models
        train_dataloader, test_dataloader = create_dataloaders(args)
        if args.model_name == 'mlp':
            print("Using MLP model")
            model = MLP(embed_dim=args.embed_dim).to(device)
        elif args.model_name == 'unet':
            print("Using UNet model")
            model = UNet(embed_dim=args.embed_dim).to(device)
        elif args.model_name == 'transformer':
            print("Using Transformer model")
            model = TransformerModel(hidden_size=args.embed_dim, nhead=args.nheads, num_layers=args.nlayers).to(device)
        else:
            raise NotImplementedError

        loss_fn = nn.MSELoss()
        optimizer = torch.optim.Adam(model.parameters(), lr=args.lr, weight_decay=args.weight_decay)
        scheduler = None

        epochs = args.epochs

        train_losses = []
        test_losses  = []
        test_acc = [[], []]
        enhanced_metrics = None  # Store final enhanced metrics

        for epoch in range(epochs):
            print(f"Epoch {epoch+1}\n-------------------------------")
            train_loss = train(dataloader=train_dataloader, model=model, loss_fn=loss_fn, optimizer=optimizer, scheduler=scheduler, device=device)
            test_loss, power_correct, area_correct = test(test_dataloader, model, loss_fn, device=device)
            train_losses.append(train_loss)
            test_losses.append(test_loss)
            test_acc[0].append(power_correct)
            test_acc[1].append(area_correct)

            # Calculate enhanced metrics for the final epoch
            if epoch == epochs - 1:
                print("\n" + "="*50)
                print("FINAL ENHANCED METRICS")
                print("="*50)
                enhanced_metrics = test_with_metrics(test_dataloader, model, loss_fn, device=device)

    plot_losses(train_losses, test_losses, epochs, args)
    plot_test_acc(test_acc, epochs, args)

    # Plot enhanced metrics if available (for PyTorch models)
    if 'enhanced_metrics' in locals() and enhanced_metrics is not None:
        plot_enhanced_metrics(enhanced_metrics, args)

    # For sklearn models, create enhanced metrics from stored values
    if args.model_name in ['linear_regression', 'gaussian_process'] and hasattr(model, 'train_r2'):
        # Create a mock enhanced metrics dict for sklearn models
        enhanced_metrics = {
            'test_loss': test_losses[0],
            'power_correct': test_acc[0][0],
            'area_correct': test_acc[1][0],
            'power_mse': test_losses[0],  # Approximation since we use MSE as loss
            'area_mse': test_losses[0],   # Approximation
            'overall_mse': test_losses[0],
            'power_r2': model.train_r2,   # Use stored R² values
            'area_r2': model.test_r2,
            'overall_r2': (model.train_r2 + model.test_r2) / 2
        }
        plot_enhanced_metrics(enhanced_metrics, args)

    # Save results with enhanced metrics
    res = []
    res.append(train_losses)
    res.append(test_losses)
    res.append(test_acc[0])
    res.append(test_acc[1])

    # Add enhanced metrics to results if available
    if 'enhanced_metrics' in locals() and enhanced_metrics is not None:
        res.append([enhanced_metrics.get('power_mse', 0)])
        res.append([enhanced_metrics.get('area_mse', 0)])
        res.append([enhanced_metrics.get('overall_mse', 0)])
        res.append([enhanced_metrics.get('power_r2', 0)])
        res.append([enhanced_metrics.get('area_r2', 0)])
        res.append([enhanced_metrics.get('overall_r2', 0)])

        index_labels = ['train losses', 'test losses', 'power acc', 'area acc',
                       'power mse', 'area mse', 'overall mse',
                       'power r2', 'area r2', 'overall r2']
    else:
        index_labels = ['train losses', 'test losses', 'power acc', 'area acc']

    pd.DataFrame(res, index=index_labels).to_csv(f'./res/{args.model_name}-{args.test_fold}.csv')



